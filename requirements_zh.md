# 保險管理系統需求文檔

## 1. 系統概述

### 1.1 業務背景
本保險管理系統是一個綜合性的保險業務處理平台，主要服務於保險公司的核心業務流程，包括保單管理、理賠處理、再保險業務、會計核算等功能。系統採用C# WinForms技術開發，基於SQL Server數據庫，為保險公司提供完整的業務管理解決方案。

### 1.2 系統目標
- 提供完整的保險業務流程管理
- 支持多種保險產品類型（車險、工程險、責任險等）
- 實現保險業務的數字化和自動化處理
- 提供準確的財務核算和報表功能
- 確保數據安全和業務合規性

### 1.3 技術架構
- **開發語言**: C# (.NET Framework 4.6.1)
- **用戶界面**: Windows Forms
- **數據庫**: Microsoft SQL Server
- **報表引擎**: Crystal Reports 13.0
- **文檔處理**: iTextSharp, Microsoft Office Interop
- **架構模式**: 三層架構（表示層、業務邏輯層、數據訪問層）

## 2. 功能需求

### 2.1 用戶管理模塊

#### 2.1.1 用戶認證
- **登錄功能**: 用戶名密碼驗證，支持密碼策略配置
- **權限控制**: 基於角色的權限管理，支持細粒度權限控制
- **會話管理**: 用戶會話跟踪和超時控制

#### 2.1.2 用戶權限
- **功能權限**: 控制用戶對不同業務模塊的訪問權限
- **數據權限**: 控制用戶對特定數據的查看和修改權限
- **操作權限**: 控制用戶的增刪改查操作權限

### 2.2 保單管理模塊

#### 2.2.1 直接保險業務
- **保單錄入**: 支持多種保險類型的保單信息錄入
  - 車險 (CAR, CSB)
  - 工程險 (CGL, CLL, FGP, PII)
  - 責任險 (EEC)
  - 財產險 (CPM, PMP, CMP)
  - 其他險種 (MYP, PAR)

#### 2.2.2 保單信息管理
- **基本信息**: 保單號、被保險人、保險期間、保險金額等
- **承保信息**: 承保條件、免賠額、保險費率等
- **附加信息**: 特別條款、附加險種、備註信息等

#### 2.2.3 批單處理
- **批單類型**: 支持多種批單類型（增加、減少、變更等）
- **批單審核**: 批單審核流程和權限控制
- **批單生效**: 批單生效處理和保單更新

### 2.3 再保險管理模塊

#### 2.3.1 再保險安排
- **比例再保險**: 成數再保險和溢額再保險
- **非比例再保險**: 超額賠款再保險和巨災再保險
- **臨時再保險**: 臨時分保安排

#### 2.3.2 再保險計算
- **保費分攤**: 自動計算再保險保費分攤
- **佣金計算**: 再保險佣金和手續費計算
- **賬單處理**: 再保險賬單生成和處理

### 2.4 理賠管理模塊

#### 2.4.1 理賠登記
- **案件登記**: 理賠案件基本信息登記
- **損失評估**: 損失金額評估和調整
- **理賠審核**: 理賠案件審核流程

#### 2.4.2 理賠處理
- **賠款計算**: 自動計算賠款金額
- **賠款支付**: 賠款支付處理和記錄
- **案件結案**: 理賠案件結案處理

#### 2.4.3 再保險理賠
- **再保險追償**: 向再保險人追償處理
- **理賠分攤**: 理賠金額在再保險人間的分攤
- **理賠結算**: 再保險理賠結算處理

### 2.5 會計核算模塊

#### 2.5.1 保費核算
- **保費收入**: 保費收入確認和記錄
- **保費分期**: 分期保費處理
- **保費退還**: 退保保費處理

#### 2.5.2 理賠核算
- **賠款支出**: 賠款支出記錄和核算
- **準備金**: 未決賠款準備金計提
- **追償收入**: 追償款項收入處理

#### 2.5.3 再保險核算
- **再保險費**: 再保險費支出核算
- **再保險佣金**: 再保險佣金收入核算
- **再保險理賠**: 再保險理賠收入核算

### 2.6 報表管理模塊

#### 2.6.1 業務報表
- **保單報表**: 保單統計和明細報表
- **理賠報表**: 理賠統計和分析報表
- **再保險報表**: 再保險業務報表

#### 2.6.2 財務報表
- **收入報表**: 保費收入和其他收入報表
- **支出報表**: 賠款支出和費用支出報表
- **利潤報表**: 承保利潤和投資收益報表

#### 2.6.3 監管報表
- **償付能力報表**: 償付能力相關報表
- **業務統計報表**: 監管要求的業務統計報表

## 3. 非功能需求

### 3.1 性能需求
- **響應時間**: 一般查詢操作響應時間不超過3秒
- **並發用戶**: 支持100個並發用戶同時操作
- **數據處理**: 支持大批量數據的導入和處理

### 3.2 安全需求
- **數據加密**: 敏感數據加密存儲
- **訪問控制**: 嚴格的用戶權限控制
- **審計日誌**: 完整的操作審計日誌

### 3.3 可靠性需求
- **系統可用性**: 系統可用性達到99.5%以上
- **數據備份**: 定期數據備份和恢復機制
- **故障恢復**: 快速故障恢復能力

### 3.4 可維護性需求
- **代碼規範**: 遵循編碼規範，便於維護
- **文檔完整**: 完整的技術文檔和用戶手冊
- **模塊化設計**: 模塊化設計，便於功能擴展

## 4. 數據結構

### 4.1 核心實體

#### 4.1.1 保單實體 (polh)
- fctlid: 保單控制ID
- fpolno: 保單號
- fclass: 險種類別
- finsd: 被保險人
- fsi: 保險金額
- fgpm: 毛保費
- fnpm: 淨保費

#### 4.1.2 理賠實體 (oclaim)
- fctlid: 理賠控制ID
- fclmno: 理賠號
- fpolno: 保單號
- faccdate: 出險日期
- flosamt: 損失金額
- fpayamt: 賠付金額

#### 4.1.3 再保險實體 (orih)
- fctlid: 再保險控制ID
- fctlid_1: 原保單ID
- frino: 再保險號
- fgpm: 再保險保費
- fnpm: 再保險淨保費

### 4.2 主數據表
- muser: 用戶信息表
- xsyscomp: 公司信息表
- xsysparm: 系統參數表

## 5. 接口規範

### 5.1 數據庫接口
- 使用ADO.NET進行數據庫訪問
- 支持事務處理和連接池
- 使用存儲過程進行複雜業務邏輯處理

### 5.2 報表接口
- 使用Crystal Reports生成報表
- 支持PDF、Excel等多種輸出格式
- 支持報表參數化和動態生成

### 5.3 外部系統接口
- 支持與A8系統的數據交換
- 支持Excel數據導入導出
- 支持Word文檔生成

## 6. 部署要求

### 6.1 硬件要求
- **服務器**: Windows Server 2012 R2或更高版本
- **內存**: 最低8GB，推薦16GB
- **存儲**: 最低500GB可用空間
- **網絡**: 千兆以太網連接

### 6.2 軟件要求
- **操作系統**: Windows 10或Windows Server 2012 R2+
- **.NET Framework**: 4.6.1或更高版本
- **數據庫**: SQL Server 2014或更高版本
- **Crystal Reports**: Runtime 13.0

### 6.3 安全配置
- 配置防火牆規則
- 設置數據庫訪問權限
- 配置SSL證書（如需要）

## 7. 技術架構詳細說明

### 7.1 系統架構圖

```mermaid
graph TB
    A[用戶界面層 - WinForms] --> B[業務邏輯層]
    B --> C[數據訪問層 - ADO.NET]
    C --> D[數據庫層 - SQL Server]

    B --> E[報表引擎 - Crystal Reports]
    B --> F[文檔處理 - iTextSharp/Office Interop]
    B --> G[外部接口 - REST API]

    H[配置管理] --> B
    I[日誌系統] --> B
    J[安全模塊] --> B
```

### 7.2 核心類結構

#### 7.2.1 數據訪問層
- **DBConnect**: 數據庫連接管理類
- **DBHelper**: 數據庫操作輔助類
- **SQLHandle**: SQL語句處理類

#### 7.2.2 業務邏輯層
- **DirectPolicy**: 直接保險業務處理
- **Reinsurance**: 再保險業務處理
- **ClaimDataFunc**: 理賠數據處理
- **GenPolicy**: 保單生成處理

#### 7.2.3 工具類
- **ExportExcel**: Excel導出功能
- **Word2PDF**: Word轉PDF功能
- **SendEmail**: 郵件發送功能
- **Validation**: 數據驗證功能

### 7.3 數據庫設計

#### 7.3.1 數據庫架構
- **live_ilodata**: 操作數據庫（保單、理賠、再保險等業務數據）
- **live_ilmdata**: 主數據庫（用戶、參數、主數據等）

#### 7.3.2 主要數據表
- **polh**: 保單主表
- **oinsint**: 保險標的表
- **oclaim**: 理賠主表
- **orih**: 再保險主表
- **oinvh**: 發票主表
- **muser**: 用戶表
- **xsyscomp**: 公司信息表

## 8. API接口文檔

### 8.1 保單管理API

#### 8.1.1 創建保單
```csharp
public string CreatePolicy(PolicyInfo policyInfo)
{
    // 保單創建邏輯
    // 返回保單控制ID
}
```

#### 8.1.2 更新保單
```csharp
public bool UpdatePolicy(string fctlid, PolicyInfo policyInfo)
{
    // 保單更新邏輯
    // 返回操作結果
}
```

#### 8.1.3 查詢保單
```csharp
public DataTable QueryPolicy(string criteria)
{
    // 保單查詢邏輯
    // 返回查詢結果
}
```

### 8.2 理賠管理API

#### 8.2.1 創建理賠案件
```csharp
public string CreateClaim(ClaimInfo claimInfo)
{
    // 理賠案件創建邏輯
    // 返回理賠控制ID
}
```

#### 8.2.2 理賠支付
```csharp
public bool ProcessPayment(string fctlid, decimal amount)
{
    // 理賠支付處理邏輯
    // 返回操作結果
}
```

### 8.3 報表生成API

#### 8.3.1 生成業務報表
```csharp
public byte[] GenerateBusinessReport(string reportType, Dictionary<string, object> parameters)
{
    // 業務報表生成邏輯
    // 返回報表文件字節數組
}
```

## 9. 運維要求

### 9.1 監控需求
- **系統性能監控**: CPU、內存、磁盤使用率監控
- **數據庫性能監控**: 查詢性能、連接數、死鎖監控
- **用戶操作監控**: 用戶登錄、關鍵操作審計
- **業務指標監控**: 保單數量、理賠金額等業務指標

### 9.2 備份策略
- **每日增量備份**: 每日凌晨2點執行增量備份
- **每週完整備份**: 每週日凌晨執行完整備份
- **異地備份存儲**: 備份文件存儲到異地災備中心
- **備份驗證**: 定期驗證備份文件完整性

### 9.3 維護計劃
- **定期系統更新**: 每季度進行系統補丁更新
- **數據庫維護**: 每月進行數據庫索引重建和統計信息更新
- **性能優化**: 每半年進行系統性能評估和優化
- **安全檢查**: 每月進行安全漏洞掃描和修復

## 10. 風險評估與應對

### 10.1 技術風險
- **數據庫性能風險**: 通過索引優化和查詢優化降低風險
- **系統穩定性風險**: 通過負載測試和容錯設計降低風險
- **數據安全風險**: 通過加密和權限控制降低風險

### 10.2 業務風險
- **數據準確性風險**: 通過數據驗證和審核流程降低風險
- **合規性風險**: 通過定期合規檢查和更新降低風險
- **操作風險**: 通過用戶培訓和操作規範降低風險

### 10.3 應急預案
- **系統故障應急**: 制定系統故障快速恢復預案
- **數據丟失應急**: 制定數據恢復和業務連續性預案
- **安全事件應急**: 制定安全事件響應和處理預案
