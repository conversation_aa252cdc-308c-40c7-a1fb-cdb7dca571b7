# Insurance Management System Requirements Document

## 1. System Overview

### 1.1 Business Background
This Insurance Management System is a comprehensive insurance business processing platform that primarily serves the core business processes of insurance companies, including policy management, claims processing, reinsurance business, and accounting functions. The system is developed using C# WinForms technology, based on SQL Server database, providing insurance companies with a complete business management solution.

### 1.2 System Objectives
- Provide complete insurance business process management
- Support multiple insurance product types (motor insurance, engineering insurance, liability insurance, etc.)
- Achieve digitalization and automation of insurance business processing
- Provide accurate financial accounting and reporting functions
- Ensure data security and business compliance

### 1.3 Technical Architecture
- **Development Language**: C# (.NET Framework 4.6.1)
- **User Interface**: Windows Forms
- **Database**: Microsoft SQL Server
- **Reporting Engine**: Crystal Reports 13.0
- **Document Processing**: iTextSharp, Microsoft Office Interop
- **Architecture Pattern**: Three-tier architecture (Presentation Layer, Business Logic Layer, Data Access Layer)

## 2. Functional Requirements

### 2.1 User Management Module

#### 2.1.1 User Authentication
- **Login Function**: Username and password verification with configurable password policies
- **Access Control**: Role-based access management with fine-grained permission control
- **Session Management**: User session tracking and timeout control

#### 2.1.2 User Permissions
- **Functional Permissions**: Control user access to different business modules
- **Data Permissions**: Control user viewing and modification rights for specific data
- **Operation Permissions**: Control user CRUD operation rights

### 2.2 Policy Management Module

#### 2.2.1 Direct Insurance Business
- **Policy Entry**: Support policy information entry for multiple insurance types
  - Motor Insurance (CAR, CSB)
  - Engineering Insurance (CGL, CLL, FGP, PII)
  - Liability Insurance (EEC)
  - Property Insurance (CPM, PMP, CMP)
  - Other Insurance Types (MYP, PAR)

#### 2.2.2 Policy Information Management
- **Basic Information**: Policy number, insured party, insurance period, sum insured, etc.
- **Underwriting Information**: Underwriting conditions, deductibles, premium rates, etc.
- **Additional Information**: Special clauses, additional coverages, remarks, etc.

#### 2.2.3 Endorsement Processing
- **Endorsement Types**: Support multiple endorsement types (addition, reduction, modification, etc.)
- **Endorsement Approval**: Endorsement approval workflow and permission control
- **Endorsement Effectiveness**: Endorsement effectiveness processing and policy updates

### 2.3 Reinsurance Management Module

#### 2.3.1 Reinsurance Arrangements
- **Proportional Reinsurance**: Quota share and surplus reinsurance
- **Non-proportional Reinsurance**: Excess of loss and catastrophe reinsurance
- **Facultative Reinsurance**: Facultative reinsurance arrangements

#### 2.3.2 Reinsurance Calculations
- **Premium Allocation**: Automatic calculation of reinsurance premium allocation
- **Commission Calculation**: Reinsurance commission and brokerage calculation
- **Account Processing**: Reinsurance account generation and processing

### 2.4 Claims Management Module

#### 2.4.1 Claims Registration
- **Case Registration**: Basic information registration for claims cases
- **Loss Assessment**: Loss amount assessment and adjustment
- **Claims Review**: Claims case review workflow

#### 2.4.2 Claims Processing
- **Settlement Calculation**: Automatic calculation of settlement amounts
- **Payment Processing**: Settlement payment processing and recording
- **Case Closure**: Claims case closure processing

#### 2.4.3 Reinsurance Claims
- **Reinsurance Recovery**: Recovery processing from reinsurers
- **Claims Allocation**: Allocation of claim amounts among reinsurers
- **Claims Settlement**: Reinsurance claims settlement processing

### 2.5 Accounting Module

#### 2.5.1 Premium Accounting
- **Premium Income**: Premium income recognition and recording
- **Installment Premiums**: Installment premium processing
- **Premium Refunds**: Cancellation premium refund processing

#### 2.5.2 Claims Accounting
- **Claims Expenses**: Claims expense recording and accounting
- **Reserves**: Outstanding claims reserve provisions
- **Recovery Income**: Recovery income processing

#### 2.5.3 Reinsurance Accounting
- **Reinsurance Premiums**: Reinsurance premium expense accounting
- **Reinsurance Commission**: Reinsurance commission income accounting
- **Reinsurance Claims**: Reinsurance claims income accounting

### 2.6 Reporting Module

#### 2.6.1 Business Reports
- **Policy Reports**: Policy statistics and detailed reports
- **Claims Reports**: Claims statistics and analysis reports
- **Reinsurance Reports**: Reinsurance business reports

#### 2.6.2 Financial Reports
- **Income Reports**: Premium income and other income reports
- **Expense Reports**: Claims expenses and operational expense reports
- **Profit Reports**: Underwriting profit and investment income reports

#### 2.6.3 Regulatory Reports
- **Solvency Reports**: Solvency-related reports
- **Business Statistics Reports**: Regulatory required business statistics reports

## 3. Non-Functional Requirements

### 3.1 Performance Requirements
- **Response Time**: General query operations response time not exceeding 3 seconds
- **Concurrent Users**: Support 100 concurrent users operating simultaneously
- **Data Processing**: Support bulk data import and processing

### 3.2 Security Requirements
- **Data Encryption**: Encrypted storage of sensitive data
- **Access Control**: Strict user permission control
- **Audit Logs**: Complete operation audit logs

### 3.3 Reliability Requirements
- **System Availability**: System availability of 99.5% or higher
- **Data Backup**: Regular data backup and recovery mechanisms
- **Fault Recovery**: Rapid fault recovery capabilities

### 3.4 Maintainability Requirements
- **Code Standards**: Follow coding standards for easy maintenance
- **Complete Documentation**: Complete technical documentation and user manuals
- **Modular Design**: Modular design for easy functional expansion

## 4. Data Structure

### 4.1 Core Entities

#### 4.1.1 Policy Entity (polh)
- fctlid: Policy control ID
- fpolno: Policy number
- fclass: Insurance class
- finsd: Insured party
- fsi: Sum insured
- fgpm: Gross premium
- fnpm: Net premium

#### 4.1.2 Claims Entity (oclaim)
- fctlid: Claims control ID
- fclmno: Claim number
- fpolno: Policy number
- faccdate: Accident date
- flosamt: Loss amount
- fpayamt: Payment amount

#### 4.1.3 Reinsurance Entity (orih)
- fctlid: Reinsurance control ID
- fctlid_1: Original policy ID
- frino: Reinsurance number
- fgpm: Reinsurance premium
- fnpm: Reinsurance net premium

### 4.2 Master Data Tables
- muser: User information table
- xsyscomp: Company information table
- xsysparm: System parameter table

## 5. Interface Specifications

### 5.1 Database Interface
- Use ADO.NET for database access
- Support transaction processing and connection pooling
- Use stored procedures for complex business logic processing

### 5.2 Reporting Interface
- Use Crystal Reports for report generation
- Support multiple output formats including PDF, Excel
- Support parameterized and dynamic report generation

### 5.3 External System Interfaces
- Support data exchange with A8 system
- Support Excel data import/export
- Support Word document generation

## 6. Deployment Requirements

### 6.1 Hardware Requirements
- **Server**: Windows Server 2012 R2 or higher
- **Memory**: Minimum 8GB, recommended 16GB
- **Storage**: Minimum 500GB available space
- **Network**: Gigabit Ethernet connection

### 6.2 Software Requirements
- **Operating System**: Windows 10 or Windows Server 2012 R2+
- **.NET Framework**: 4.6.1 or higher
- **Database**: SQL Server 2014 or higher
- **Crystal Reports**: Runtime 13.0

### 6.3 Security Configuration
- Configure firewall rules
- Set database access permissions
- Configure SSL certificates (if required)

## 7. Detailed Technical Architecture

### 7.1 System Architecture Diagram

```mermaid
graph TB
    A[Presentation Layer - WinForms] --> B[Business Logic Layer]
    B --> C[Data Access Layer - ADO.NET]
    C --> D[Database Layer - SQL Server]

    B --> E[Reporting Engine - Crystal Reports]
    B --> F[Document Processing - iTextSharp/Office Interop]
    B --> G[External Interface - REST API]

    H[Configuration Management] --> B
    I[Logging System] --> B
    J[Security Module] --> B
```

### 7.2 Core Class Structure

#### 7.2.1 Data Access Layer
- **DBConnect**: Database connection management class
- **DBHelper**: Database operation helper class
- **SQLHandle**: SQL statement processing class

#### 7.2.2 Business Logic Layer
- **DirectPolicy**: Direct insurance business processing
- **Reinsurance**: Reinsurance business processing
- **ClaimDataFunc**: Claims data processing
- **GenPolicy**: Policy generation processing

#### 7.2.3 Utility Classes
- **ExportExcel**: Excel export functionality
- **Word2PDF**: Word to PDF conversion functionality
- **SendEmail**: Email sending functionality
- **Validation**: Data validation functionality

### 7.3 Database Design

#### 7.3.1 Database Architecture
- **live_ilodata**: Operational database (policy, claims, reinsurance business data)
- **live_ilmdata**: Master database (users, parameters, master data)

#### 7.3.2 Main Data Tables
- **polh**: Policy master table
- **oinsint**: Insured object table
- **oclaim**: Claims master table
- **orih**: Reinsurance master table
- **oinvh**: Invoice master table
- **muser**: User table
- **xsyscomp**: Company information table

## 8. API Documentation

### 8.1 Policy Management API

#### 8.1.1 Create Policy
```csharp
public string CreatePolicy(PolicyInfo policyInfo)
{
    // Policy creation logic
    // Returns policy control ID
}
```

#### 8.1.2 Update Policy
```csharp
public bool UpdatePolicy(string fctlid, PolicyInfo policyInfo)
{
    // Policy update logic
    // Returns operation result
}
```

#### 8.1.3 Query Policy
```csharp
public DataTable QueryPolicy(string criteria)
{
    // Policy query logic
    // Returns query results
}
```

### 8.2 Claims Management API

#### 8.2.1 Create Claim Case
```csharp
public string CreateClaim(ClaimInfo claimInfo)
{
    // Claim case creation logic
    // Returns claim control ID
}
```

#### 8.2.2 Process Payment
```csharp
public bool ProcessPayment(string fctlid, decimal amount)
{
    // Payment processing logic
    // Returns operation result
}
```

### 8.3 Report Generation API

#### 8.3.1 Generate Business Report
```csharp
public byte[] GenerateBusinessReport(string reportType, Dictionary<string, object> parameters)
{
    // Business report generation logic
    // Returns report file byte array
}
```

## 9. Operations Requirements

### 9.1 Monitoring Requirements
- **System Performance Monitoring**: CPU, memory, disk usage monitoring
- **Database Performance Monitoring**: Query performance, connection count, deadlock monitoring
- **User Operation Monitoring**: User login, critical operation auditing
- **Business Metrics Monitoring**: Policy count, claim amounts and other business metrics

### 9.2 Backup Strategy
- **Daily Incremental Backups**: Execute incremental backups daily at 2 AM
- **Weekly Full Backups**: Execute full backups every Sunday morning
- **Off-site Backup Storage**: Store backup files at off-site disaster recovery center
- **Backup Verification**: Regularly verify backup file integrity

### 9.3 Maintenance Plan
- **Regular System Updates**: Quarterly system patch updates
- **Database Maintenance**: Monthly database index rebuilding and statistics updates
- **Performance Optimization**: Semi-annual system performance evaluation and optimization
- **Security Checks**: Monthly security vulnerability scanning and remediation

## 10. Risk Assessment and Mitigation

### 10.1 Technical Risks
- **Database Performance Risk**: Mitigate through index optimization and query optimization
- **System Stability Risk**: Mitigate through load testing and fault-tolerant design
- **Data Security Risk**: Mitigate through encryption and access control

### 10.2 Business Risks
- **Data Accuracy Risk**: Mitigate through data validation and review processes
- **Compliance Risk**: Mitigate through regular compliance checks and updates
- **Operational Risk**: Mitigate through user training and operational procedures

### 10.3 Emergency Response Plans
- **System Failure Emergency**: Develop rapid system recovery emergency plans
- **Data Loss Emergency**: Develop data recovery and business continuity plans
- **Security Incident Emergency**: Develop security incident response and handling plans
